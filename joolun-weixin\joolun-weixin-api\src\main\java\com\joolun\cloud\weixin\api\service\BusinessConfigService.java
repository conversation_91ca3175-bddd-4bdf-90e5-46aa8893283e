package com.joolun.cloud.weixin.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joolun.cloud.weixin.common.entity.BusinessConfig;

/**
 * 业务配置
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
public interface BusinessConfigService extends IService<BusinessConfig> {

    /**
     * 根据配置键获取配置值
     * @param configKey 配置键
     * @return 配置值
     */
    String getConfigValue(String configKey);
}
