package com.joolun.cloud.weixin.api.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.cloud.common.data.tenant.TenantContextHolder;
import com.joolun.cloud.weixin.api.mapper.BusinessConfigMapper;
import com.joolun.cloud.weixin.api.service.BusinessConfigService;
import com.joolun.cloud.weixin.common.entity.BusinessConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 业务配置
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@Slf4j
@Service
public class BusinessConfigServiceImpl extends ServiceImpl<BusinessConfigMapper, BusinessConfig> implements BusinessConfigService {

    @Override
    public String getConfigValue(String configKey) {
        BusinessConfig config = this.getOne(Wrappers.<BusinessConfig>lambdaQuery()
                .eq(BusinessConfig::getTenantId, TenantContextHolder.getTenantId())
                .eq(BusinessConfig::getConfigKey, configKey)
                .eq(BusinessConfig::getDelFlag, "0"));
        
        return config != null ? config.getConfigValue() : null;
    }
}
