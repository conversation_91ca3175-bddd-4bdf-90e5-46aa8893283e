package com.joolun.cloud.weixin.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.weixin.common.dto.AuctionCalendarDTO;
import com.joolun.cloud.weixin.common.entity.AppointRules;
import com.joolun.cloud.weixin.common.entity.AuctionCalendar;
import com.joolun.cloud.weixin.common.vo.AppointInfoVO;

import java.util.List;

/**
 * 档期时间表
 *
 * <AUTHOR>
 * @date 2022-02-19 14:33:35
 */
public interface AuctionCalendarService extends IService<AuctionCalendar> {

	/**
	 * 得到list 条件
	 * @param auctionCalendarDTO
	 * @return
	 */
	List<AuctionCalendar> getListByCon(AuctionCalendarDTO auctionCalendarDTO);
	/**
	 * 新增档期
	 * @param auctionCalendarDTO
	 * @return
	 */
	R saveAppoint(AuctionCalendarDTO auctionCalendarDTO);


	/**
	 * 得到档期信息
	 * @param auctionCalendarDTO
	 * @return
	 */
	AppointInfoVO getAppointInfo(AuctionCalendarDTO auctionCalendarDTO);

	/**
	 * 重选选档
	 * @param auctionCalendarDTO
	 * @return
	 */
	R resetAppoint(AuctionCalendarDTO auctionCalendarDTO);

	/**
	 * 得到选择档期数
	 * @param auctionCalendarDTO
	 * @return
	 */
	Integer getAppointNum(AuctionCalendarDTO auctionCalendarDTO, AppointRules appointRules);

	/**
	 * 取消预约档期
	 * @param auctionCalendarDTO
	 * @return
	 */
	R cancelAppoint(AuctionCalendarDTO auctionCalendarDTO);

	/**
	 * 强制删除档期
	 * @param auctionCalendarDTO
	 * @return
	 */
	R forceDeleteAppoint(AuctionCalendarDTO auctionCalendarDTO);
}